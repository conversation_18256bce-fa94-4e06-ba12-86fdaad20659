apply plugin: 'java'

version = '1.0.0'
group = 'com.example.mobcontroller'

repositories {
    mavenCentral()
}

// For now, let's just compile the Java files without Forge dependencies
// This will help us identify any syntax errors in the code
compileJava {
    options.compilerArgs += ['-Xlint:unchecked', '-Xlint:deprecation']
}

jar {
    manifest {
        attributes([
                "Specification-Title"     : "mobcontroller",
                "Specification-Vendor"   : "YourName",
                "Specification-Version"  : "1",
                "Implementation-Title"   : project.name,
                "Implementation-Version" : project.version,
                "Implementation-Vendor"  : "YourName",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}
